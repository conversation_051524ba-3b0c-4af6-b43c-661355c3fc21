services:
  api-gateway:
    build:
      context: .
      dockerfile: apps/api-gateway/Dockerfile
    ports:
      - '3000:3000'
    depends_on:
      - user-management
      - apiary-management
  user-management:
    build:
      context: .
      dockerfile: apps/user-management/Dockerfile
    ports:
      - '3001:3001'
    depends_on:
      - db
  apiary-management:
    build:
      context: .
      dockerfile: apps/apiary-management/Dockerfile
    ports:
      - '3002:3002'
    depends_on:
      - db
  sensor-ingestion:
    build:
      context: .
      dockerfile: apps/sensor-ingestion/Dockerfile
    ports:
      - '3003:3003'
    depends_on:
      - rabbitmq
      - influxdb
  websocket:
    build:
      context: .
      dockerfile: apps/websocket/Dockerfile
    ports:
      - '3004:3004'
    depends_on:
      - rabbitmq
  db:
    image: postgres:13
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: smart-hive-db
    ports:
      - '5432:5432'
  influxdb:
    image: influxdb:2.0
    ports:
      - '8086:8086'
    volumes:
      - influxdb-data:/var/lib/influxdb2
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - '5672:5672'
      - '35672:15672'
volumes:
  postgres-data:
  influxdb-data:
  rabbitmq-data:
