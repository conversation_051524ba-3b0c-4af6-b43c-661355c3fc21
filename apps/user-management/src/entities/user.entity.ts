import { <PERSON>ti<PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>umn, ManyToOne } from 'typeorm';
import { Tenant } from './tenant.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class User {
  @ApiProperty({
    description: 'The unique identifier of the user.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The name of the user.',
    example: '<PERSON>',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'The email of the user.',
    example: '<EMAIL>',
  })
  @Column({ unique: true })
  email: string;

  @Column()
  passwordHash: string;

  @ManyToOne(() => Tenant, (tenant) => tenant.users)
  tenant: Tenant;
}
