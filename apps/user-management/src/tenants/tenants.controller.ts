import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { TenantsService } from './tenants.service';
import { Tenant } from '../entities/tenant.entity';

@Controller('tenants')
export class TenantsController {
  constructor(private readonly tenantsService: TenantsService) {}

  @Post()
  create(@Body() tenant: Partial<Tenant>): Promise<Tenant> {
    return this.tenantsService.create(tenant);
  }

  @Get()
  findAll(): Promise<Tenant[]> {
    return this.tenantsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Tenant> {
    return this.tenantsService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() tenant: Partial<Tenant>,
  ): Promise<Tenant> {
    return this.tenantsService.update(id, tenant);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.tenantsService.remove(id);
  }
}
