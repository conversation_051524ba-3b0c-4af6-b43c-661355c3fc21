import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxy } from '@nestjs/microservices';
import { InfluxDB, Point, WriteApi } from '@influxdata/influxdb-client';

@Injectable()
export class SensorIngestionService {
  private readonly influxDB: InfluxDB;
  private readonly writeApi: WriteApi;

  constructor(
    private readonly configService: ConfigService,
    @Inject('SENSOR_DATA_QUEUE') private readonly client: ClientProxy,
  ) {
    const url = this.configService.get<string>('INFLUXDB_URL');
    const token = this.configService.get<string>('INFLUXDB_TOKEN');
    const org = this.configService.get<string>('INFLUXDB_ORG');
    const bucket = this.configService.get<string>('INFLUXDB_BUCKET');

    if (!url || !token || !org || !bucket) {
      throw new Error('InfluxDB environment variables not set');
    }

    this.influxDB = new InfluxDB({ url, token });
    this.writeApi = this.influxDB.getWriteApi(org, bucket);
  }

  async handleSensorData(data: any): Promise<{ message: string }> {
    const point = new Point('sensor_data')
      .tag('sensor_id', data.sensor_id)
      .floatField('temperature', data.temperature)
      .floatField('humidity', data.humidity)
      .timestamp(new Date());

    this.writeApi.writePoint(point);
    await this.writeApi.flush();

    this.client.emit('sensor_data_processed', data);

    return { message: 'Sensor data processed successfully' };
  }
}
