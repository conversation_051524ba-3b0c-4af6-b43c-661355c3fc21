import { Module } from '@nestjs/common';
import { SensorIngestionController } from './sensor-ingestion.controller';
import { SensorIngestionService } from './sensor-ingestion.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './apps/sensor-ingestion/.env',
    }),
    ClientsModule.registerAsync([
      {
        name: 'SENSOR_DATA_QUEUE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const rmqUrl = configService.get<string>('RABBITMQ_URL');
          if (!rmqUrl) {
            throw new Error('RABBITMQ_URL environment variable not set');
          }
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rmqUrl],
              queue: 'sensor_data',
              queueOptions: {
                durable: false,
              },
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [SensorIngestionController],
  providers: [SensorIngestionService],
})
export class SensorIngestionModule {}
