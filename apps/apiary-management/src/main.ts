import { NestFactory } from '@nestjs/core';
import { ApiaryManagementModule } from './apiary-management.module';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(ApiaryManagementModule);
  const configService = app.get(ConfigService);
  const rmqUrl = configService.get<string>('RABBITMQ_URL');
  if (!rmqUrl) {
    throw new Error('RABBITMQ_URL environment variable not set');
  }

  const config = new DocumentBuilder()
    .setTitle('Apiary Management API')
    .setDescription('API documentation for the Apiary Management microservice')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const microservice =
    await NestFactory.createMicroservice<MicroserviceOptions>(
      ApiaryManagementModule,
      {
        transport: Transport.RMQ,
        options: {
          urls: [rmqUrl],
          queue: 'apiary_management_queue',
          queueOptions: {
            durable: false,
          },
        },
      },
    );

  await app.listen(3002);
  await microservice.listen();
}
void bootstrap();
