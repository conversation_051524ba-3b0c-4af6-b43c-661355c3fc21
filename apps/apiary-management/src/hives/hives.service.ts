import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Hive } from '../entities/hive.entity';

@Injectable()
export class HivesService {
  constructor(
    @InjectRepository(Hive)
    private hivesRepository: Repository<Hive>,
  ) {}

  findAll(): Promise<Hive[]> {
    return this.hivesRepository.find();
  }

  async findOne(id: string): Promise<Hive> {
    const hive = await this.hivesRepository.findOneBy({ id });
    if (!hive) {
      throw new NotFoundException(`Hive with ID ${id} not found`);
    }
    return hive;
  }

  async create(hive: Partial<Hive>): Promise<Hive> {
    const newHive = this.hivesRepository.create(hive);
    return this.hivesRepository.save(newHive);
  }

  async update(id: string, hive: Partial<Hive>): Promise<Hive> {
    const existingHive = await this.findOne(id);
    await this.hivesRepository.update(id, hive);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.hivesRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Hive with ID ${id} not found`);
    }
  }
}
