import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { HivesService } from './hives.service';
import { Hive } from '../entities/hive.entity';

@Controller('hives')
export class HivesController {
  constructor(private readonly hivesService: HivesService) {}

  @Post()
  create(@Body() hive: Partial<Hive>): Promise<Hive> {
    return this.hivesService.create(hive);
  }

  @Get()
  findAll(): Promise<Hive[]> {
    return this.hivesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Hive> {
    return this.hivesService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() hive: Partial<Hive>): Promise<Hive> {
    return this.hivesService.update(id, hive);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.hivesService.remove(id);
  }
}
