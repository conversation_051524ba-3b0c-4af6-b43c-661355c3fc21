import { Test, TestingModule } from '@nestjs/testing';
import { ApiaryManagementController } from './apiary-management.controller';
import { ApiaryManagementService } from './apiary-management.service';

describe('ApiaryManagementController', () => {
  let apiaryManagementController: ApiaryManagementController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [ApiaryManagementController],
      providers: [ApiaryManagementService],
    }).compile();

    apiaryManagementController = app.get<ApiaryManagementController>(
      ApiaryManagementController,
    );
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(apiaryManagementController.getHello()).toBe('Hello World!');
    });
  });
});
