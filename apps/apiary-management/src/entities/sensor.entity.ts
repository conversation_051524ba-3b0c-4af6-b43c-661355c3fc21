import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { HiveSensor } from './hive-sensor.entity';
import { ApiProperty } from '@nestjs/swagger';

export enum SensorType {
  TEMPERATURE = 'temperature',
  HUMIDITY = 'humidity',
  WEIGHT = 'weight',
  SOUND = 'sound',
  VIBRATION = 'vibration',
  LIGHT = 'light',
}

export enum SensorStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MAINTENANCE = 'maintenance',
  FAULTY = 'faulty',
}

@Entity()
export class Sensor {
  @ApiProperty({
    description: 'The unique identifier of the sensor.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The device ID of the sensor.',
    example: 'TEMP_001',
  })
  @Column({ unique: true })
  deviceId: string;

  @ApiProperty({
    description: 'The name of the sensor.',
    example: 'Temperature Sensor 1',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'The type of the sensor.',
    example: 'temperature',
    enum: SensorType,
  })
  @Column({
    type: 'enum',
    enum: SensorType,
  })
  type: SensorType;

  @ApiProperty({
    description: 'The manufacturer of the sensor.',
    example: 'SensorTech Inc.',
  })
  @Column({ nullable: true })
  manufacturer: string;

  @ApiProperty({
    description: 'The model of the sensor.',
    example: 'ST-TEMP-001',
  })
  @Column({ nullable: true })
  model: string;

  @ApiProperty({
    description: 'The firmware version of the sensor.',
    example: '1.2.3',
  })
  @Column({ nullable: true })
  firmwareVersion: string;

  @ApiProperty({
    description: 'The status of the sensor.',
    example: 'active',
    enum: SensorStatus,
  })
  @Column({
    type: 'enum',
    enum: SensorStatus,
    default: SensorStatus.ACTIVE,
  })
  status: SensorStatus;

  @ApiProperty({
    description: 'The battery level of the sensor (0-100).',
    example: 85,
  })
  @Column({ type: 'int', nullable: true })
  batteryLevel: number;

  @ApiProperty({
    description: 'The last communication timestamp.',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Column({ type: 'timestamp', nullable: true })
  lastCommunication: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => HiveSensor, (hiveSensor) => hiveSensor.sensor)
  hiveSensors: HiveSensor[];
}
