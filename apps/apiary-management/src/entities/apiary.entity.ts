import { <PERSON>ti<PERSON>, PrimaryGeneratedC<PERSON>umn, Column, OneToMany } from 'typeorm';
import { Hive } from './hive.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Apiary {
  @ApiProperty({
    description: 'The unique identifier of the apiary.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The name of the apiary.',
    example: 'My First Apiary',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'The location of the apiary.',
    example: 'My Backyard',
  })
  @Column()
  location: string;

  @OneToMany(() => Hive, (hive) => hive.apiary)
  hives: Hive[];
}
