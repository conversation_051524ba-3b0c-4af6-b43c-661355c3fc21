import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Apiary } from './apiary.entity';
import { Colony } from './colony.entity';
import { Inspection } from './inspection.entity';
import { Harvest } from './harvest.entity';
import { HiveSensor } from './hive-sensor.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Hive {
  @ApiProperty({
    description: 'The unique identifier of the hive.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The name of the hive.',
    example: 'Hive 1',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'The installation location of the hive.',
    example: 'North corner of the apiary',
  })
  @Column({ nullable: true })
  installationLocation: string;

  @ApiProperty({
    description: 'The date when the hive was installed.',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Column({ type: 'timestamp', nullable: true })
  installationDate: Date;

  @ApiProperty({
    description: 'The apiary code for this hive.',
    example: 'AP001',
  })
  @Column({ nullable: true })
  apiaryCode: string;

  @ApiProperty({
    description: 'QR code data for the hive.',
    example: 'base64-encoded-qr-code',
  })
  @Column({ type: 'text', nullable: true })
  qrCode: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Apiary, (apiary) => apiary.hives)
  apiary: Apiary;

  @OneToMany(() => Colony, (colony) => colony.hive)
  colonies: Colony[];

  @OneToMany(() => Inspection, (inspection) => inspection.hive)
  inspections: Inspection[];

  @OneToMany(() => Harvest, (harvest) => harvest.hive)
  harvests: Harvest[];

  @OneToMany(() => HiveSensor, (hiveSensor) => hiveSensor.hive)
  hiveSensors: HiveSensor[];
}
