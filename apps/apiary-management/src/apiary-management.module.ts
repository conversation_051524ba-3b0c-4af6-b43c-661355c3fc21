import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiaryManagementController } from './apiary-management.controller';
import { ApiaryManagementService } from './apiary-management.service';
import { Apiary } from './entities/apiary.entity';
import { Hive } from './entities/hive.entity';
import { Colony } from './entities/colony.entity';
import { Inspection } from './entities/inspection.entity';
import { Harvest } from './entities/harvest.entity';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DB_HOST'),
        port: configService.get<number>('DB_PORT'),
        username: configService.get<string>('DB_USERNAME'),
        password: configService.get<string>('DB_PASSWORD'),
        database: configService.get<string>('DB_DATABASE'),
        entities: [Apiary, Hive, Colony, Inspection, Harvest],
        synchronize: true,
        schema: 'apiary_management',
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [ApiaryManagementController],
  providers: [ApiaryManagementService],
})
export class ApiaryManagementModule {}
