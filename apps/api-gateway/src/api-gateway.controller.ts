import { <PERSON>, Post, Body, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@Controller()
export class ApiGatewayController {
  constructor(
    @Inject('USER_MANAGEMENT_SERVICE')
    private readonly userManagementService: ClientProxy,
    @Inject('APIARY_MANAGEMENT_SERVICE')
    private readonly apiaryManagementService: ClientProxy,
  ) {}

  @Post('users')
  createUser(@Body() createUserDto: any) {
    return this.userManagementService.send(
      { cmd: 'create_user' },
      createUserDto,
    );
  }

  @Post('apiaries')
  createApiary(@Body() createApiaryDto: any) {
    return this.apiaryManagementService.send(
      { cmd: 'create_apiary' },
      createApiaryDto,
    );
  }
}
