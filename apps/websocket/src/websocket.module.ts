import { Module } from '@nestjs/common';
import { WebsocketController } from './websocket.controller';
import { WebsocketService } from './websocket.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { WebsocketGateway } from './websocket.gateway';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './apps/websocket/.env',
    }),
    ClientsModule.registerAsync([
      {
        name: 'SENSOR_DATA_QUEUE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const rmqUrl = configService.get<string>('RABBITMQ_URL');
          if (!rmqUrl) {
            throw new Error('RABBITMQ_URL environment variable not set');
          }
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rmqUrl],
              queue: 'sensor_data_processed',
              queueOptions: {
                durable: false,
              },
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [WebsocketController],
  providers: [WebsocketService, WebsocketGateway],
})
export class WebsocketModule {}
