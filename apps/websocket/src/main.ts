import { NestFactory } from '@nestjs/core';
import { WebsocketModule } from './websocket.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(WebsocketModule);

  const config = new DocumentBuilder()
    .setTitle('Websocket API')
    .setDescription('API documentation for the Websocket microservice')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(3004);
}
void bootstrap();
