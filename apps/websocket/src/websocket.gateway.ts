import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
} from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Controller } from '@nestjs/common';
import {
  Ctx,
  MessagePattern,
  Payload,
  RmqContext,
} from '@nestjs/microservices';

@WebSocketGateway()
export class WebsocketGateway {
  @WebSocketServer()
  server: Server;

  @MessagePattern('sensor_data_processed')
  handleSensorDataProcessed(@Payload() data: any, @Ctx() context: RmqContext) {
    this.server.emit('sensor_data', data);
  }
}
