{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/the-apiary/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/the-apiary/tsconfig.app.json"}, "monorepo": true, "root": "apps/the-apiary", "projects": {"api-gateway": {"type": "application", "root": "apps/api-gateway", "entryFile": "main", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"tsConfigPath": "apps/api-gateway/tsconfig.app.json"}}, "apiary-management": {"type": "application", "root": "apps/apiary-management", "entryFile": "main", "sourceRoot": "apps/apiary-management/src", "compilerOptions": {"tsConfigPath": "apps/apiary-management/tsconfig.app.json"}}, "sensor-ingestion": {"type": "application", "root": "apps/sensor-ingestion", "entryFile": "main", "sourceRoot": "apps/sensor-ingestion/src", "compilerOptions": {"tsConfigPath": "apps/sensor-ingestion/tsconfig.app.json"}}, "the-apiary": {"type": "application", "root": "apps/the-apiary", "entryFile": "main", "sourceRoot": "apps/the-apiary/src", "compilerOptions": {"tsConfigPath": "apps/the-apiary/tsconfig.app.json"}}, "user-management": {"type": "application", "root": "apps/user-management", "entryFile": "main", "sourceRoot": "apps/user-management/src", "compilerOptions": {"tsConfigPath": "apps/user-management/tsconfig.app.json"}}, "websocket": {"type": "application", "root": "apps/websocket", "entryFile": "main", "sourceRoot": "apps/websocket/src", "compilerOptions": {"tsConfigPath": "apps/websocket/tsconfig.app.json"}}}}