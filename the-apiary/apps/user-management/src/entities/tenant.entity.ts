import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
} from 'typeorm';
import { User } from './user.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Tenant {
  @ApiProperty({
    description: 'The unique identifier of the tenant.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The name of the tenant.',
    example: 'My Company',
  })
  @Column()
  name: string;

  @ManyToOne(() => Tenant, (tenant) => tenant.children)
  parent: Tenant;

  @OneToMany(() => Tenant, (tenant) => tenant.parent)
  children: Tenant[];

  @OneToMany(() => User, (user) => user.tenant)
  users: User[];
}
