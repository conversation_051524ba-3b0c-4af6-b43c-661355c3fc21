import { NestFactory } from '@nestjs/core';
import { UserManagementModule } from './user-management.module';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(UserManagementModule);
  const configService = app.get(ConfigService);
  const rmqUrl = configService.get<string>('RABBITMQ_URL');
  if (!rmqUrl) {
    throw new Error('RABBITMQ_URL environment variable not set');
  }

  const config = new DocumentBuilder()
    .setTitle('User Management API')
    .setDescription('API documentation for the User Management microservice')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const microservice =
    await NestFactory.createMicroservice<MicroserviceOptions>(
      UserManagementModule,
      {
        transport: Transport.RMQ,
        options: {
          urls: [rmqUrl],
          queue: 'user_management_queue',
          queueOptions: {
            durable: false,
          },
        },
      },
    );

  await app.listen(3001);
  await microservice.listen();
}
void bootstrap();
