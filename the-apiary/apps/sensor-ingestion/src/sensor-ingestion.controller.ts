import { Controller, Post, Body } from '@nestjs/common';
import { SensorIngestionService } from './sensor-ingestion.service';

@Controller()
export class SensorIngestionController {
  constructor(
    private readonly sensorIngestionService: SensorIngestionService,
  ) {}

  @Post('sensor-data')
  handleSensorData(@Body() data: any) {
    return this.sensorIngestionService.handleSensorData(data);
  }
}
