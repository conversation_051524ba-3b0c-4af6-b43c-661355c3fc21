import { Test, TestingModule } from '@nestjs/testing';
import { SensorIngestionController } from './sensor-ingestion.controller';
import { SensorIngestionService } from './sensor-ingestion.service';

describe('SensorIngestionController', () => {
  let sensorIngestionController: SensorIngestionController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [SensorIngestionController],
      providers: [SensorIngestionService],
    }).compile();

    sensorIngestionController = app.get<SensorIngestionController>(
      SensorIngestionController,
    );
  });

  describe('root', () => {
    it('should return "Hello World!"', () => {
      expect(sensorIngestionController.getHello()).toBe('Hello World!');
    });
  });
});
