import { NestFactory } from '@nestjs/core';
import { SensorIngestionModule } from './sensor-ingestion.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(SensorIngestionModule);

  const config = new DocumentBuilder()
    .setTitle('Sensor Ingestion API')
    .setDescription('API documentation for the Sensor Ingestion microservice')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(3003);
}
void bootstrap();
