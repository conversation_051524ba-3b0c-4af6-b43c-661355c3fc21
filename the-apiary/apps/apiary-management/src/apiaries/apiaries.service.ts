import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Apiary } from '../entities/apiary.entity';

@Injectable()
export class ApiariesService {
  constructor(
    @InjectRepository(Apiary)
    private apiariesRepository: Repository<Apiary>,
  ) {}

  findAll(): Promise<Apiary[]> {
    return this.apiariesRepository.find();
  }

  async findOne(id: string): Promise<Apiary> {
    const apiary = await this.apiariesRepository.findOneBy({ id });
    if (!apiary) {
      throw new NotFoundException(`Apiary with ID ${id} not found`);
    }
    return apiary;
  }

  async create(apiary: Partial<Apiary>): Promise<Apiary> {
    const newApiary = this.apiariesRepository.create(apiary);
    return this.apiariesRepository.save(newApiary);
  }

  async update(id: string, apiary: Partial<Apiary>): Promise<Apiary> {
    const existingApiary = await this.findOne(id);
    await this.apiariesRepository.update(id, apiary);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.apiariesRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Apiary with ID ${id} not found`);
    }
  }
}
