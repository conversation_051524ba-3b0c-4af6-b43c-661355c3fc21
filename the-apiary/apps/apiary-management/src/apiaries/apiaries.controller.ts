import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { ApiariesService } from './apiaries.service';
import { Apiary } from '../entities/apiary.entity';
import { MessagePattern } from '@nestjs/microservices';

@Controller('apiaries')
export class ApiariesController {
  constructor(private readonly apiariesService: ApiariesService) {}

  @MessagePattern({ cmd: 'create_apiary' })
  createApiary(createApiaryDto: Partial<Apiary>): Promise<Apiary> {
    return this.apiariesService.create(createApiaryDto);
  }

  @Post()
  create(@Body() apiary: Partial<Apiary>): Promise<Apiary> {
    return this.apiariesService.create(apiary);
  }

  @Get()
  findAll(): Promise<Apiary[]> {
    return this.apiariesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Apiary> {
    return this.apiariesService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() apiary: Partial<Apiary>,
  ): Promise<Apiary> {
    return this.apiariesService.update(id, apiary);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.apiariesService.remove(id);
  }
}
