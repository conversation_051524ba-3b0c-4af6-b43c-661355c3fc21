import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiariesService } from './apiaries.service';
import { ApiariesController } from './apiaries.controller';
import { Apiary } from '../entities/apiary.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Apiary])],
  providers: [ApiariesService],
  controllers: [ApiariesController],
})
export class ApiariesModule {}
