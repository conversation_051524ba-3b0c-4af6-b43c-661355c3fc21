import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Colony } from '../entities/colony.entity';

@Injectable()
export class ColoniesService {
  constructor(
    @InjectRepository(Colony)
    private coloniesRepository: Repository<Colony>,
  ) {}

  findAll(): Promise<Colony[]> {
    return this.coloniesRepository.find();
  }

  async findOne(id: string): Promise<Colony> {
    const colony = await this.coloniesRepository.findOneBy({ id });
    if (!colony) {
      throw new NotFoundException(`Colony with ID ${id} not found`);
    }
    return colony;
  }

  async create(colony: Partial<Colony>): Promise<Colony> {
    const newColony = this.coloniesRepository.create(colony);
    return this.coloniesRepository.save(newColony);
  }

  async update(id: string, colony: Partial<Colony>): Promise<Colony> {
    const existingColony = await this.findOne(id);
    await this.coloniesRepository.update(id, colony);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.coloniesRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Colony with ID ${id} not found`);
    }
  }
}
