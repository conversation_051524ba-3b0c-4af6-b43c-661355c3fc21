import { <PERSON>ti<PERSON>, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Hive } from './hive.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Colony {
  @ApiProperty({
    description: 'The unique identifier of the colony.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The name of the queen.',
    example: 'Queen Bee',
  })
  @Column()
  queenName: string;

  @ApiProperty({
    description: 'The birth date of the queen.',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Column()
  queenBirthDate: Date;

  @ManyToOne(() => Hive, (hive) => hive.colonies)
  hive: Hive;
}
