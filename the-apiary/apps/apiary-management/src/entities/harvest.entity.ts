import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Hive } from './hive.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Harvest {
  @ApiProperty({
    description: 'The unique identifier of the harvest.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The date of the harvest.',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Column()
  harvestDate: Date;

  @ApiProperty({
    description: 'The quantity of the harvest.',
    example: 10,
  })
  @Column()
  quantity: number;

  @ManyToOne(() => Hive, (hive) => hive.harvests)
  hive: Hive;
}
