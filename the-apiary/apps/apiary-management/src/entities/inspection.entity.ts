import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Hive } from './hive.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Inspection {
  @ApiProperty({
    description: 'The unique identifier of the inspection.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The date of the inspection.',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Column()
  inspectionDate: Date;

  @ApiProperty({
    description: 'Whether the queen was seen during the inspection.',
    example: true,
  })
  @Column()
  queenSeen: boolean;

  @ApiProperty({
    description: 'The brood pattern observed during the inspection.',
    example: 'Good',
  })
  @Column()
  broodPattern: string;

  @ApiProperty({
    description: 'The condition of the hive observed during the inspection.',
    example: 'Strong',
  })
  @Column()
  hiveCondition: string;

  @ApiProperty({
    description: 'Any pests observed during the inspection.',
    example: 'Varroa mites',
  })
  @Column()
  pests: string;

  @ApiProperty({
    description: 'Any diseases observed during the inspection.',
    example: 'Chalkbrood',
  })
  @Column()
  diseases: string;

  @ApiProperty({
    description: 'The actions taken during the inspection.',
    example: 'Treated for varroa mites',
  })
  @Column()
  actionsTaken: string;

  @ManyToOne(() => Hive, (hive) => hive.inspections)
  hive: Hive;
}
