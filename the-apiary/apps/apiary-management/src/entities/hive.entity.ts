import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { Apiary } from './apiary.entity';
import { Colony } from './colony.entity';
import { Inspection } from './inspection.entity';
import { Harvest } from './harvest.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Hive {
  @ApiProperty({
    description: 'The unique identifier of the hive.',
    example: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The name of the hive.',
    example: 'Hive 1',
  })
  @Column()
  name: string;

  @ManyToOne(() => Apiary, (apiary) => apiary.hives)
  apiary: Apiary;

  @OneToMany(() => Colony, (colony) => colony.hive)
  colonies: Colony[];

  @OneToMany(() => Inspection, (inspection) => inspection.hive)
  inspections: Inspection[];

  @OneToMany(() => Harvest, (harvest) => harvest.hive)
  harvests: Harvest[];
}
