import { Module } from '@nestjs/common';
import { ApiGatewayController } from './api-gateway.controller';
import { ApiGatewayService } from './api-gateway.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './apps/api-gateway/.env',
    }),
    ClientsModule.registerAsync([
      {
        name: 'USER_MANAGEMENT_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const rmqUrl = configService.get<string>('RABBITMQ_URL');
          if (!rmqUrl) {
            throw new Error('RABBITMQ_URL environment variable not set');
          }
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rmqUrl],
              queue: 'user_management_queue',
            },
          };
        },
        inject: [ConfigService],
      },
      {
        name: 'APIARY_MANAGEMENT_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => {
          const rmqUrl = configService.get<string>('RABBITMQ_URL');
          if (!rmqUrl) {
            throw new Error('RABBITMQ_URL environment variable not set');
          }
          return {
            transport: Transport.RMQ,
            options: {
              urls: [rmqUrl],
              queue: 'apiary_management_queue',
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [ApiGatewayController],
  providers: [ApiGatewayService],
})
export class ApiGatewayModule {}
